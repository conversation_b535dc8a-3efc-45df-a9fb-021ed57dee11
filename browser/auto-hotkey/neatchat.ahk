#Requires Autohotkey v2

; win+R 执行shell:startup，然后把这个脚本拖进去，就能开机自启

exePath := "D:\chatgpt-browser\myBrowser.exe"
threshold := 400
maxPressDuration := 200  ; 最大按键持续时间（毫秒），超过此时间视为长按

lastLUp := 0
LCtrlLastDown := 0

; 记录 down 状态，忽略没有对应 down 的 up（常见于 RDP 合成 up）
LCtrlIsDown := false

; 捕获 Left Control down（~ 保留系统原有行为）
~LControl:: {
    global LCtrlIsDown, LCtrlLastDown
    LCtrlIsDown := true
    ; 记录按下时间
    LCtrlLastDown := A_TickCount
    ; 如果需要调试解除下面注释
    ; Tooltip("LCtrl down")
    return
}

~LControl up:: {
    global lastLUp, threshold, exePath, LCtrlIsDown, LCtrlLastDown, maxPressDuration
    if (LCtrlIsDown) {
        curr := A_TickCount
        pressDuration := curr - LCtrlLastDown

        ; 检查是否为短按
        if (pressDuration <= maxPressDuration) {
            ; 这是一次短按，检查是否为双击的第二次
            if (lastLUp > 0 && curr - lastLUp < threshold) {
                ; 检查没有其它修饰键按下（避免在组合键使用时触发）
                if !(GetKeyState("LShift","P") || GetKeyState("RShift","P")
                    || GetKeyState("LAlt","P") || GetKeyState("RAlt","P")
                    || GetKeyState("LWin","P") || GetKeyState("RWin","P")
                    || GetKeyState("RControl","P")) {
                    exeDir := RegExReplace(exePath, "\\[^\\]+$")
                    if (StrLen(exeDir) == 0) {
			    	    exeDir := A_ScriptDir
			    	}
                    Run(exePath, exeDir)
                    lastLUp := 0  ; 重置，避免连续触发
                    return
                }
            }
            ; 更新最后一次短按的时间
            lastLUp := curr
        } else {
            ; 这是一次长按，重置计时器
            lastLUp := 0
        }
	} else {
        ; 没有对应 down，可能是 RDP 合成的 up —— 忽略
        ; Tooltip("忽略合成 LControl up")
    }
    ; 重置状态
    LCtrlIsDown := false
    LCtrlLastDown := 0
    return
}
